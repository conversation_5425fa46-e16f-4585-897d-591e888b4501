#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from youtube_recon.impl.gemini_video_recon import VideoDataByYtDlp, VideoSummary, VideoData

class TestVideoData:
    def test_init(self):
        yt_dlp_data = VideoDataByYtDlp(
            categories=["Science & Technology"],
            channel="Test Channel",
            description="Test description",
            duration=1709,
            fulltitle="Test Video",
            id="test123",
            language="en",
            tags=["test", "video"],
            thumbnail="https://example.com/thumbnail.jpg",
            timestamp=1234567890,
            uploader="Test Uploader",
            uploader_url="https://example.com/uploader",
            webpage_url="https://www.youtube.com/watch?v=test123"
        )
        summary_data = VideoSummary(
            summary="Test summary",
            main_theme="Test main theme",
            key_points=["Test key point 1", "Test key point 2"]
        )
        video_data = VideoData(yt_dlp_data=yt_dlp_data, summary_data=summary_data)

        assert video_data.categories == ["Science & Technology"]
        assert video_data.channel == "Test Channel"
        assert video_data.description == "Test description"
        assert video_data.duration == 1709
        assert video_data.fulltitle == "Test Video"
        assert video_data.id == "test123"
        assert video_data.language == "en"
        assert video_data.tags == ["test", "video"]
        assert video_data.thumbnail == "https://example.com/thumbnail.jpg"
        assert video_data.timestamp == 1234567890
        assert video_data.uploader == "Test Uploader"
        assert video_data.uploader_url == "https://example.com/uploader"
        assert video_data.webpage_url == "https://www.youtube.com/watch?v=test123"
        assert video_data.summary == "Test summary"
        assert video_data.main_theme == "Test main theme"
        assert video_data.key_points == ["Test key point 1", "Test key point 2"]
