#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from abc import ABC, abstractmethod
import asyncio
import click
from icecream import ic
from logear.autolog_exception import adapt_autolog_exception
from logging import Logger
from typing import cast, Dict, Generic, Optional, Type, Tuple, Union
from urllib3.util import Url
from youtube_recon.video_recon import VideoReconBase, P, S


class GroupExt(click.Group):
    def add_command(self, cmd, name=None):
        click.Group.add_command(self, cmd, name=name)
        for param in self.params:
            cmd.params.append(param)


@adapt_autolog_exception()
class VideoReconCLIBase(ABC, Generic[P, S]):
    """
    A reusable CLI adapter for any VideoReconBaseShell implementation.
    """

    def __init__(self, video_recon: VideoReconBase, prop_ret_type: Type[P], summary_ret_type: Type[S]):
        self.video_recon = video_recon
        self.prop_ret_type = prop_ret_type
        self.summary_ret_type = summary_ret_type

    @abstractmethod
    def output_summary(self, summaries: Dict[Url, S], path: Optional[str] = None, format: str = 'text'):
        raise NotImplementedError()

    @abstractmethod
    def output_detail(self, meta: Dict[Url, Tuple[P, S]], path: Optional[str] = None, format: str = 'text'):
        raise NotImplementedError()

    @abstractmethod
    def output_playlist_urls(self, playlist: Url, props: Tuple[P], path: Optional[str] = None, format: str = 'text', extra: bool=False):
        raise NotImplementedError()

    @abstractmethod
    def output_video_summaries_in_playlist(
            self, playlist: Url, summaries: Dict[Url, S], path: Optional[str] = None, format: str = 'text'):
        raise NotImplementedError()

    @abstractmethod
    def output_video_details_in_playlist(
            self, playlist: Url, v_metas: Dict[Url, Tuple[P, S]], path: Optional[str] = None, format: str = 'text'):
        raise NotImplementedError()

    def show_errors(self, errors: Dict[Url, Exception]):
        contents = "Failed to fetch data for the following URL(s):\n\n"
        for url, err in errors.items():
            contents = contents + "URL: {0}\n".format(url)
            contents = contents + "Error: {0}\n\n".format(err)
        print(contents)

    @abstractmethod
    def validate_url(self, url: str) -> Url:
        raise NotImplementedError()

    @property
    def AsyncTaskFailureReturnCode(self) -> int:
        return 10

    def run(self):
        def validate_urls(ctx, param, urls: Union[str, Tuple[str, ...]]) -> Union[Url, Tuple[Url, ...]]:
            if isinstance(urls, str):
                return self.validate_url(url=urls)

            url_objs = []
            for url in urls:
                url_obj = self.validate_url(url=url)
                url_objs.append(url_obj)

            return tuple(url_objs)

        @click.group()
        def cli():
            """Video Recon CLI"""
            pass

        @cli.group()
        def video():
            """Video-related commands"""
            pass

        @click.command(name='video_common', cls=GroupExt)
        @click.option('-u', '--url', 'urls',  multiple=True, required=True, callback=validate_urls,
                      help='Video URL. Can be given multiple times.')
        @click.option('-f', '--format', 'format', required=False,
                      type=click.Choice(['json', 'text'], case_sensitive=False), default='text',
                      help='Output format.')
        @click.option('-p', '--path', 'path',
                      required=False, default=None, type=click.Path(file_okay=True, dir_okay=False, writable=True),
                      help='Output path.')
        def video_common():
            pass

        @video_common.command("summary", help='Get summary for each given YouTube video URL.')
        def get_summaries(urls: Tuple[Url, ...], format: str='text', path: Optional[str]=None):
            """
            Fetch and display the video summary for a given URL.
            """
            v_sources = {url: None for url in urls}

            urled_summaries, errors = asyncio.run(self.video_recon.async_get_video_summaries(v_sources=v_sources))

            self.output_summary(summaries=urled_summaries, path=path, format=format)

            if len(errors) > 0:
                self.show_errors(errors=errors)
                if len(errors) == len(v_sources):
                    exit(code=self.AsyncTaskFailureReturnCode)

        video.add_command(get_summaries)

        @video_common.command("detail", help='Get full information for each given YouTube video URL.')
        def get_detail(urls: Tuple[Url, ...], format: str='text', path: Optional[str]=None):
            urled_meta, errors = asyncio.run(self.video_recon.async_batch_get_video_meta(urls_or_props=urls))

            self.output_detail(meta=urled_meta, path=path, format=format)

            if len(errors) > 0:
                self.show_errors(errors=errors)
                if len(errors) == len(urls):
                    exit(code=self.AsyncTaskFailureReturnCode)

        video.add_command(get_detail)

        @cli.group()
        def playlist():
            """Playlist-related commands"""
            pass

        @click.command(name='playlist_common', cls=GroupExt)
        @click.option('-u', '--url', 'url',  multiple=False, required=True, callback=validate_urls,
                      help='Playlist URL.')
        @click.option('-f', '--format', 'format',
                      required=False, type=click.Choice(['json', 'text'], case_sensitive=False),
                      default='text', show_default=True,
                      help='Output format.')
        @click.option('-p', '--path', 'path',
                      required=False, default=None, type=click.Path(file_okay=True, dir_okay=False, writable=True),
                      help='Output path.')
        def playlist_common():
            pass

        @playlist_common.command("urls", help='Get member urls of a given YouTube playlist.')
        @click.option('-e', '--extra', 'extra',
                      required=False, is_flag=True, default=False,
                      help='Flag to show extra key data of each member video. Default is Urls only.')
        def get_urls_in_playlist(url: Url, extra: bool=False, format: str = 'text', path: Optional[str] = None):
            props = self.video_recon.get_playlist_items(playlist=url)
            self.output_playlist_urls(playlist=url, props=props, path=path, format=format, extra=extra)

        playlist.add_command(get_urls_in_playlist)

        @playlist_common.command("summaries", help='Get summaries for each member video of a given YouTube playlist.')
        def get_video_summaries_in_playlist(url: Url, format: str = 'text', path: Optional[str] = None):
            props = self.video_recon.get_playlist_items(playlist=url)

            v_sources = ic({self.video_recon.get_video_url(v_prop=prop): prop for prop in props})
            urled_summaries, errors = asyncio.run(
                self.video_recon.async_get_video_summaries(v_sources=v_sources))

            self.output_video_summaries_in_playlist(playlist=url, summaries=urled_summaries, path=path, format=format)

            if len(errors) > 0:
                print()
                self.show_errors(errors=errors)
                if len(errors) == len(props):
                    exit(code=self.AsyncTaskFailureReturnCode)

        playlist.add_command(get_video_summaries_in_playlist)

        @playlist_common.command("details", help='Get full information for each member video of a given YouTube playlist.')
        def get_video_details_in_playlist(url: Url, format: str = 'text', path: Optional[str] = None):
            logger = cast(Logger, self.grab_logger())
            props = self.video_recon.get_playlist_items(playlist=url)
            logger.debug(f"Got {len(props)} playlist items")

            v_sources = {self.video_recon.get_video_url(v_prop=prop): prop for prop in props}
            logger.debug(f"Parsed meta information of {len(v_sources)} video(s)")

            urled_summaries, errors = asyncio.run(
                self.video_recon.async_get_video_summaries(v_sources=v_sources),
                debug=True)
            logger.debug(f"async_get_video_summaries returned. Results: {len(urled_summaries)}, Errors: {len(errors)}")

            v_metas = {}
            for v_url in urled_summaries.keys():
                v_prop = v_sources[v_url]
                v_metas.update({v_url: (v_prop, urled_summaries[v_url])})
            logger.debug(f"Created {len(v_metas)} video metas")

            logger.debug("About to call output_video_details_in_playlist")
            self.output_video_details_in_playlist(playlist=url, v_metas=v_metas, path=path, format=format)
            logger.debug("output_video_details_in_playlist completed")

            if len(errors) > 0:
                logger.debug("About to show errors")
                self.show_errors(errors=errors)
                logger.debug("show_errors completed")
                if len(errors) == len(props):
                    logger.debug("All tasks failed, exiting")
                    exit(code=self.AsyncTaskFailureReturnCode)

            logger.debug("get_video_details_in_playlist completed successfully")

        playlist.add_command(get_video_details_in_playlist)

        cli()  # Invoke the CLI
